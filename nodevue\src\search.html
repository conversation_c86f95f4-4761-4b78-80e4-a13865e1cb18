<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>查询页面 - 多页面管理系统</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background-color: #f5f7fa;
                color: #333;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            header {
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                color: white;
                padding: 20px 0;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .logo {
                font-size: 28px;
                font-weight: bold;
            }

            nav ul {
                display: flex;
                list-style: none;
            }

            nav li {
                margin-left: 20px;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 8px 15px;
                border-radius: 5px;
                transition: background-color 0.3s;
            }

            nav a:hover,
            nav a.active {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .card {
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                padding: 20px;
                margin-bottom: 20px;
            }

            h2 {
                color: #2c3e50;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #f0f0f0;
            }

            .card h3 {
                color: #3498db;
                margin-bottom: 15px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            input,
            select,
            textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
            }

            button {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #2980b9;
            }

            .btn-warning {
                background: #f39c12;
            }

            .btn-warning:hover {
                background: #d35400;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }

            th,
            td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }

            th {
                background-color: #f8f9fa;
                font-weight: 600;
            }

            tr:hover {
                background-color: #f5f7fa;
            }

            .action-buttons {
                display: flex;
                gap: 10px;
            }

            .action-buttons button {
                padding: 8px 12px;
                font-size: 14px;
            }

            footer {
                text-align: center;
                margin-top: 50px;
                padding: 20px;
                color: #7f8c8d;
                border-top: 1px solid #eee;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <div class="header-content">
                    <div class="logo">多页面管理系统</div>
                    <nav>
                        <ul>
                            <li>
                                <a href="/index.html">首页</a>
                            </li>
                            <li>
                                <a href="/add.html">增加页面</a>
                            </li>
                            <li>
                                <a href="/delete.html">删除页面</a>
                            </li>
                            <li>
                                <a href="/edit.html">修改页面</a>
                            </li>
                            <li>
                                <a href="/search.html" class="active"
                                    >查询页面</a
                                >
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>

            <main>
                <h2>查询数据</h2>
                <div class="card">
                    <h3>数据查询</h3>
                    <div class="form-group">
                        <label for="search-keyword">关键词</label>
                        <input
                            type="text"
                            id="search-keyword"
                            placeholder="请输入关键词"
                        />
                    </div>
                    <div class="form-group">
                        <label for="search-category">类别</label>
                        <select id="search-category">
                            <option value="">所有类别</option>
                            <option value="product">产品</option>
                            <option value="service">服务</option>
                            <option value="technology">技术</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="search-status">状态</label>
                        <select id="search-status">
                            <option value="">所有状态</option>
                            <option value="normal">正常</option>
                            <option value="pause">暂停</option>
                            <option value="stop">停止</option>
                        </select>
                    </div>
                    <button type="button" onclick="searchData()">查询</button>
                    <button
                        type="button"
                        style="margin-left: 10px"
                        onclick="resetSearch()"
                    >
                        重置
                    </button>
                </div>

                <div class="card">
                    <h3>查询结果</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>类别</th>
                                <th>价格</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1001</td>
                                <td>示例产品A</td>
                                <td>产品</td>
                                <td>¥199.00</td>
                                <td>正常</td>
                                <td>2023-05-15</td>
                                <td>
                                    <div class="action-buttons">
                                        <button>查看详情</button>
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>1002</td>
                                <td>技术服务B</td>
                                <td>服务</td>
                                <td>¥599.00</td>
                                <td>正常</td>
                                <td>2023-05-14</td>
                                <td>
                                    <div class="action-buttons">
                                        <button>查看详情</button>
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>1003</td>
                                <td>技术方案C</td>
                                <td>技术</td>
                                <td>¥1299.00</td>
                                <td>暂停</td>
                                <td>2023-05-10</td>
                                <td>
                                    <div class="action-buttons">
                                        <button>查看详情</button>
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="margin-top: 20px; text-align: center">
                        <button>上一页</button>
                        <span style="margin: 0 15px">第 1 页，共 5 页</span>
                        <button>下一页</button>
                    </div>
                </div>
            </main>

            <footer>
                <p>© 2023 多页面管理系统 | 设计用于演示多页面切换功能</p>
            </footer>
        </div>

        <script>
            document.addEventListener("DOMContentLoaded", function () {
                loadAllData();
            });

            // 加载所有数据
            function loadAllData() {
                fetch("/api/data")
                    .then((response) => response.json())
                    .then((data) => {
                        updateResultTable(data);
                    })
                    .catch((error) => {
                        console.error("Error loading data:", error);
                    });
            }

            // 搜索数据
            function searchData() {
                const keyword = document.getElementById("search-keyword").value;
                const category =
                    document.getElementById("search-category").value;
                const status = document.getElementById("search-status").value;

                let url = "/api/search?";
                const params = [];

                if (keyword)
                    params.push(`keyword=${encodeURIComponent(keyword)}`);
                if (category)
                    params.push(`category=${encodeURIComponent(category)}`);
                if (status) params.push(`status=${encodeURIComponent(status)}`);

                url += params.join("&");

                fetch(url)
                    .then((response) => response.json())
                    .then((data) => {
                        updateResultTable(data);
                    })
                    .catch((error) => {
                        console.error("Error searching data:", error);
                    });
            }

            // 重置搜索
            function resetSearch() {
                document.getElementById("search-keyword").value = "";
                document.getElementById("search-category").value = "";
                document.getElementById("search-status").value = "";
                loadAllData();
            }

            // 更新结果表格
            function updateResultTable(data) {
                const tbody = document.querySelector(".card table tbody");
                tbody.innerHTML = "";

                if (data.length === 0) {
                    const row = document.createElement("tr");
                    row.innerHTML =
                        '<td colspan="7" style="text-align: center;">暂无数据</td>';
                    tbody.appendChild(row);
                    return;
                }

                data.forEach((item) => {
                    const row = document.createElement("tr");
                    row.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${getCategoryText(item.category)}</td>
                        <td>¥${item.price.toFixed(2)}</td>
                        <td>${getStatusText(item.status)}</td>
                        <td>${item.createTime}</td>
                        <td>
                            <div class="action-buttons">
                                <button onclick="viewDetail(${
                                    item.id
                                })">查看详情</button>
                                <button class="btn-warning" onclick="editItem(${
                                    item.id
                                })">修改</button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 获取类别中文名称
            function getCategoryText(category) {
                const categoryMap = {
                    product: "产品",
                    service: "服务",
                    technology: "技术",
                    other: "其他",
                };
                return categoryMap[category] || category;
            }

            // 获取状态中文名称
            function getStatusText(status) {
                const statusMap = {
                    normal: "正常",
                    pause: "暂停",
                    stop: "停止",
                };
                return statusMap[status] || status;
            }

            // 查看详情
            function viewDetail(id) {
                alert(`查看详情功能 - ID: ${id}`);
            }

            // 编辑项目
            function editItem(id) {
                window.location.href = `/edit.html?id=${id}`;
            }
        </script>
    </body>
</html>
