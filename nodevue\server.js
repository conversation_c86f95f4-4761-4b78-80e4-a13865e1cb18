const http = require("http");
const fs = require("fs");
const url = require("url");
const path = require("path");

const server = http.createServer(function (req, res) {
    let pathname = url.parse(req.url).pathname;

    // 根路径重定向到首页
    if (pathname === "/") {
        pathname = "/index.html";
    }

    // 处理HTML页面路由
    if (pathname === "/index.html") {
        serveHtmlFile(res, "./src/index.html");
    } else if (pathname === "/add.html") {
        serveHtmlFile(res, "./src/add.html");
    } else if (pathname === "/delete.html") {
        serveHtmlFile(res, "./src/delete.html");
    } else if (pathname === "/edit.html") {
        serveHtmlFile(res, "./src/edit.html");
    } else if (pathname === "/search.html") {
        serveHtmlFile(res, "./src/search.html");
    } else if (pathname === "/user/add") {
        res.writeHead(200, {
            "content-type": "text/html",
        });
        res.write("user/add");
        res.end();
    } else {
        // 尝试提供静态文件
        const filePath = path.join(__dirname, "src", pathname);

        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                // 文件不存在，返回404
                res.writeHead(404, {
                    "content-type": "text/html",
                });
                res.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>404 - 页面未找到</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              h1 { color: #e74c3c; }
              a { color: #3498db; text-decoration: none; }
              a:hover { text-decoration: underline; }
            </style>
          </head>
          <body>
            <h1>404 - 页面未找到</h1>
            <p>抱歉，您访问的页面不存在。</p>
            <p><a href="/">返回首页</a></p>
          </body>
          </html>
        `);
                res.end();
            } else {
                // 文件存在，提供静态文件服务
                serveStaticFile(res, filePath);
            }
        });
    }
});

// 提供HTML文件的函数
function serveHtmlFile(res, filePath) {
    fs.readFile(filePath, "utf8", (err, data) => {
        if (err) {
            res.writeHead(500, {
                "content-type": "text/html",
            });
            res.write("Internal Server Error");
            res.end();
            return;
        }

        res.writeHead(200, {
            "content-type": "text/html; charset=utf-8",
        });
        res.write(data);
        res.end();
    });
}

// 提供静态文件的函数
function serveStaticFile(res, filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        ".html": "text/html",
        ".js": "text/javascript",
        ".css": "text/css",
        ".json": "application/json",
        ".png": "image/png",
        ".jpg": "image/jpg",
        ".gif": "image/gif",
        ".svg": "image/svg+xml",
        ".wav": "audio/wav",
        ".mp4": "video/mp4",
        ".woff": "application/font-woff",
        ".ttf": "application/font-ttf",
        ".eot": "application/vnd.ms-fontobject",
        ".otf": "application/font-otf",
        ".wasm": "application/wasm",
    };

    const contentType = mimeTypes[ext] || "application/octet-stream";

    fs.readFile(filePath, (err, content) => {
        if (err) {
            res.writeHead(500);
            res.end(
                "Sorry, check with the site admin for error: " +
                    err.code +
                    " ..\n"
            );
        } else {
            res.writeHead(200, { "Content-Type": contentType });
            res.end(content, "utf-8");
        }
    });
}

server.listen(3001, function () {
    console.log("Server is listening on port 3001");
    console.log("Visit: http://localhost:3001");
    console.log("Alternative: http://127.0.0.1:3001");
});
