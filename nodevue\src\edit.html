<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>修改页面 - 多页面管理系统</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background-color: #f5f7fa;
                color: #333;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            header {
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                color: white;
                padding: 20px 0;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .logo {
                font-size: 28px;
                font-weight: bold;
            }

            nav ul {
                display: flex;
                list-style: none;
            }

            nav li {
                margin-left: 20px;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 8px 15px;
                border-radius: 5px;
                transition: background-color 0.3s;
            }

            nav a:hover,
            nav a.active {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .card {
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                padding: 20px;
                margin-bottom: 20px;
            }

            h2 {
                color: #2c3e50;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #f0f0f0;
            }

            .card h3 {
                color: #3498db;
                margin-bottom: 15px;
            }

            .search-box {
                display: flex;
                margin-bottom: 20px;
            }

            .search-box input {
                flex: 1;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            .search-box button {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            .form-group {
                margin-bottom: 20px;
            }

            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            input,
            select,
            textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
            }

            button {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #2980b9;
            }

            .btn-warning {
                background: #f39c12;
            }

            .btn-warning:hover {
                background: #d35400;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }

            th,
            td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }

            th {
                background-color: #f8f9fa;
                font-weight: 600;
            }

            tr:hover {
                background-color: #f5f7fa;
            }

            .action-buttons {
                display: flex;
                gap: 10px;
            }

            .action-buttons button {
                padding: 8px 12px;
                font-size: 14px;
            }

            footer {
                text-align: center;
                margin-top: 50px;
                padding: 20px;
                color: #7f8c8d;
                border-top: 1px solid #eee;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <div class="header-content">
                    <div class="logo">多页面管理系统</div>
                    <nav>
                        <ul>
                            <li><a href="/index.html">首页</a></li>
                            <li><a href="/add.html">增加页面</a></li>
                            <li><a href="/delete.html">删除页面</a></li>
                            <li>
                                <a href="/edit.html" class="active">修改页面</a>
                            </li>
                            <li><a href="/search.html">查询页面</a></li>
                        </ul>
                    </nav>
                </div>
            </header>

            <main>
                <h2>修改数据</h2>
                <div class="card">
                    <h3>选择要修改的数据</h3>
                    <div class="search-box">
                        <input type="text" placeholder="搜索要修改的数据..." />
                        <button>搜索</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>类别</th>
                                <th>价格</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1001</td>
                                <td>示例产品A</td>
                                <td>产品</td>
                                <td>¥199.00</td>
                                <td>正常</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>1002</td>
                                <td>技术服务B</td>
                                <td>服务</td>
                                <td>¥599.00</td>
                                <td>正常</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>1003</td>
                                <td>技术方案C</td>
                                <td>技术</td>
                                <td>¥1299.00</td>
                                <td>暂停</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="card">
                    <h3>修改数据表单</h3>
                    <form id="edit-form">
                        <div class="form-group">
                            <label for="edit-id">数据ID</label>
                            <input
                                type="text"
                                id="edit-id"
                                placeholder="请先选择要修改的数据"
                                readonly
                            />
                        </div>
                        <div class="form-group">
                            <label for="edit-name">名称</label>
                            <input
                                type="text"
                                id="edit-name"
                                placeholder="请输入名称"
                            />
                        </div>
                        <div class="form-group">
                            <label for="edit-category">类别</label>
                            <select id="edit-category">
                                <option value="">请选择类别</option>
                                <option value="product">产品</option>
                                <option value="service">服务</option>
                                <option value="technology">技术</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-description">描述</label>
                            <textarea
                                id="edit-description"
                                rows="4"
                                placeholder="请输入描述信息"
                            ></textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-price">价格</label>
                            <input
                                type="number"
                                id="edit-price"
                                placeholder="请输入价格"
                            />
                        </div>
                        <div class="form-group">
                            <label for="edit-status">状态</label>
                            <select id="edit-status">
                                <option value="normal">正常</option>
                                <option value="pause">暂停</option>
                                <option value="stop">停止</option>
                            </select>
                        </div>
                        <button type="submit" class="btn-warning">
                            保存修改
                        </button>
                        <button type="button" style="margin-left: 10px">
                            取消
                        </button>
                    </form>
                </div>
            </main>

            <footer>
                <p>© 2023 多页面管理系统 | 设计用于演示多页面切换功能</p>
            </footer>
        </div>

        <script>
            document
                .getElementById("edit-form")
                .addEventListener("submit", function (e) {
                    e.preventDefault();
                    alert("数据修改成功！");
                });
        </script>
    </body>
</html>
