<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>增加页面 - 多页面管理系统</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background-color: #f5f7fa;
                color: #333;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            header {
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                color: white;
                padding: 20px 0;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .logo {
                font-size: 28px;
                font-weight: bold;
            }

            nav ul {
                display: flex;
                list-style: none;
            }

            nav li {
                margin-left: 20px;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 8px 15px;
                border-radius: 5px;
                transition: background-color 0.3s;
            }

            nav a:hover,
            nav a.active {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .card {
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                padding: 20px;
                margin-bottom: 20px;
            }

            h2 {
                color: #2c3e50;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #f0f0f0;
            }

            .card h3 {
                color: #3498db;
                margin-bottom: 15px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            input,
            select,
            textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
            }

            button {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #2980b9;
            }

            .btn-success {
                background: #2ecc71;
            }

            .btn-success:hover {
                background: #27ae60;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }

            th,
            td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }

            th {
                background-color: #f8f9fa;
                font-weight: 600;
            }

            tr:hover {
                background-color: #f5f7fa;
            }

            footer {
                text-align: center;
                margin-top: 50px;
                padding: 20px;
                color: #7f8c8d;
                border-top: 1px solid #eee;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <div class="header-content">
                    <div class="logo">多页面管理系统</div>
                    <nav>
                        <ul>
                            <li>
                                <a href="/index.html">首页</a>
                            </li>
                            <li>
                                <a href="/add.html" class="active">增加页面</a>
                            </li>
                            <li>
                                <a href="/delete.html">删除页面</a>
                            </li>
                            <li>
                                <a href="/edit.html">修改页面</a>
                            </li>
                            <li>
                                <a href="/search.html">查询页面</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>

            <main>
                <h2>增加数据</h2>
                <div class="card">
                    <h3>添加新数据</h3>
                    <form id="add-form">
                        <div class="form-group">
                            <label for="name">名称</label>
                            <input
                                type="text"
                                id="name"
                                placeholder="请输入名称"
                                required
                            />
                        </div>
                        <div class="form-group">
                            <label for="category">类别</label>
                            <select id="category" required>
                                <option value="">请选择类别</option>
                                <option value="product">产品</option>
                                <option value="service">服务</option>
                                <option value="technology">技术</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="description">描述</label>
                            <textarea
                                id="description"
                                rows="4"
                                placeholder="请输入描述信息"
                            ></textarea>
                        </div>
                        <div class="form-group">
                            <label for="price">价格</label>
                            <input
                                type="number"
                                id="price"
                                placeholder="请输入价格"
                            />
                        </div>
                        <button type="submit" class="btn-success">
                            添加数据
                        </button>
                    </form>
                </div>

                <div class="card">
                    <h3>最近添加的数据</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>类别</th>
                                <th>价格</th>
                                <th>添加时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1001</td>
                                <td>示例产品A</td>
                                <td>产品</td>
                                <td>¥199.00</td>
                                <td>2023-05-15</td>
                            </tr>
                            <tr>
                                <td>1002</td>
                                <td>技术服务B</td>
                                <td>服务</td>
                                <td>¥599.00</td>
                                <td>2023-05-14</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </main>

            <footer>
                <p>© 2023 多页面管理系统 | 设计用于演示多页面切换功能</p>
            </footer>
        </div>

        <script>
            document.addEventListener("DOMContentLoaded", function () {
                loadRecentData();
            });

            document
                .getElementById("add-form")
                .addEventListener("submit", function (e) {
                    e.preventDefault();

                    const formData = {
                        name: document.getElementById("name").value,
                        category: document.getElementById("category").value,
                        description:
                            document.getElementById("description").value,
                        price:
                            parseFloat(
                                document.getElementById("price").value
                            ) || 0,
                        status: "normal",
                    };

                    fetch("/api/data", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify(formData),
                    })
                        .then((response) => response.json())
                        .then((data) => {
                            alert("数据添加成功！");
                            this.reset();
                            loadRecentData(); // 重新加载最近数据
                        })
                        .catch((error) => {
                            console.error("Error:", error);
                            alert("添加失败，请重试！");
                        });
                });

            // 加载最近添加的数据
            function loadRecentData() {
                fetch("/api/data")
                    .then((response) => response.json())
                    .then((data) => {
                        const recentData = data.slice(-5).reverse(); // 最近5条数据
                        updateRecentTable(recentData);
                    })
                    .catch((error) => {
                        console.error("Error loading recent data:", error);
                    });
            }

            // 更新最近数据表格
            function updateRecentTable(data) {
                const tbody = document.querySelector(".card table tbody");
                tbody.innerHTML = "";

                data.forEach((item) => {
                    const row = document.createElement("tr");
                    row.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${getCategoryText(item.category)}</td>
                        <td>¥${item.price.toFixed(2)}</td>
                        <td>${item.createTime}</td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 获取类别中文名称
            function getCategoryText(category) {
                const categoryMap = {
                    product: "产品",
                    service: "服务",
                    technology: "技术",
                    other: "其他",
                };
                return categoryMap[category] || category;
            }
        </script>
    </body>
</html>
