<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>修改页面 - 多页面管理系统</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background-color: #f5f7fa;
                color: #333;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            header {
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                color: white;
                padding: 20px 0;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .logo {
                font-size: 28px;
                font-weight: bold;
            }

            nav ul {
                display: flex;
                list-style: none;
            }

            nav li {
                margin-left: 20px;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 8px 15px;
                border-radius: 5px;
                transition: background-color 0.3s;
            }

            nav a:hover,
            nav a.active {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .card {
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                padding: 20px;
                margin-bottom: 20px;
            }

            h2 {
                color: #2c3e50;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #f0f0f0;
            }

            .card h3 {
                color: #3498db;
                margin-bottom: 15px;
            }

            .search-box {
                display: flex;
                margin-bottom: 20px;
            }

            .search-box input {
                flex: 1;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            .search-box button {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            .form-group {
                margin-bottom: 20px;
            }

            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            input,
            select,
            textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
            }

            button {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #2980b9;
            }

            .btn-warning {
                background: #f39c12;
            }

            .btn-warning:hover {
                background: #d35400;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }

            th,
            td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }

            th {
                background-color: #f8f9fa;
                font-weight: 600;
            }

            tr:hover {
                background-color: #f5f7fa;
            }

            .action-buttons {
                display: flex;
                gap: 10px;
            }

            .action-buttons button {
                padding: 8px 12px;
                font-size: 14px;
            }

            footer {
                text-align: center;
                margin-top: 50px;
                padding: 20px;
                color: #7f8c8d;
                border-top: 1px solid #eee;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <div class="header-content">
                    <div class="logo">多页面管理系统</div>
                    <nav>
                        <ul>
                            <li>
                                <a href="/index.html">首页</a>
                            </li>
                            <li>
                                <a href="/add.html">增加页面</a>
                            </li>
                            <li>
                                <a href="/delete.html">删除页面</a>
                            </li>
                            <li>
                                <a href="/edit.html" class="active">修改页面</a>
                            </li>
                            <li>
                                <a href="/search.html">查询页面</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>

            <main>
                <h2>修改数据</h2>
                <div class="card">
                    <h3>选择要修改的数据</h3>
                    <div class="search-box">
                        <input type="text" placeholder="搜索要修改的数据..." />
                        <button>搜索</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>类别</th>
                                <th>价格</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1001</td>
                                <td>示例产品A</td>
                                <td>产品</td>
                                <td>¥199.00</td>
                                <td>正常</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>1002</td>
                                <td>技术服务B</td>
                                <td>服务</td>
                                <td>¥599.00</td>
                                <td>正常</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>1003</td>
                                <td>技术方案C</td>
                                <td>技术</td>
                                <td>¥1299.00</td>
                                <td>暂停</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-warning">
                                            修改
                                        </button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="card">
                    <h3>修改数据表单</h3>
                    <form id="edit-form">
                        <div class="form-group">
                            <label for="edit-id">数据ID</label>
                            <input
                                type="text"
                                id="edit-id"
                                placeholder="请先选择要修改的数据"
                                readonly
                            />
                        </div>
                        <div class="form-group">
                            <label for="edit-name">名称</label>
                            <input
                                type="text"
                                id="edit-name"
                                placeholder="请输入名称"
                            />
                        </div>
                        <div class="form-group">
                            <label for="edit-category">类别</label>
                            <select id="edit-category">
                                <option value="">请选择类别</option>
                                <option value="product">产品</option>
                                <option value="service">服务</option>
                                <option value="technology">技术</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-description">描述</label>
                            <textarea
                                id="edit-description"
                                rows="4"
                                placeholder="请输入描述信息"
                            ></textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-price">价格</label>
                            <input
                                type="number"
                                id="edit-price"
                                placeholder="请输入价格"
                            />
                        </div>
                        <div class="form-group">
                            <label for="edit-status">状态</label>
                            <select id="edit-status">
                                <option value="normal">正常</option>
                                <option value="pause">暂停</option>
                                <option value="stop">停止</option>
                            </select>
                        </div>
                        <button type="submit" class="btn-warning">
                            保存修改
                        </button>
                        <button type="button" style="margin-left: 10px">
                            取消
                        </button>
                    </form>
                </div>
            </main>

            <footer>
                <p>© 2023 多页面管理系统 | 设计用于演示多页面切换功能</p>
            </footer>
        </div>

        <script>
            let currentEditId = null;

            document.addEventListener("DOMContentLoaded", function () {
                loadAllData();
                setupEventListeners();

                // 检查URL参数中是否有ID
                const urlParams = new URLSearchParams(window.location.search);
                const id = urlParams.get("id");
                if (id) {
                    loadItemForEdit(parseInt(id));
                }
            });

            function setupEventListeners() {
                document
                    .getElementById("edit-form")
                    .addEventListener("submit", function (e) {
                        e.preventDefault();
                        saveEdit();
                    });

                // 搜索功能
                document
                    .querySelector(".search-box button")
                    .addEventListener("click", searchData);

                // 取消按钮
                document
                    .querySelector('button[type="button"]')
                    .addEventListener("click", function () {
                        clearForm();
                    });
            }

            // 加载所有数据
            function loadAllData() {
                fetch("/api/data")
                    .then((response) => response.json())
                    .then((data) => {
                        updateDataTable(data);
                    })
                    .catch((error) => {
                        console.error("Error loading data:", error);
                    });
            }

            // 搜索数据
            function searchData() {
                const keyword =
                    document.querySelector(".search-box input").value;
                if (!keyword.trim()) {
                    loadAllData();
                    return;
                }

                fetch(`/api/search?keyword=${encodeURIComponent(keyword)}`)
                    .then((response) => response.json())
                    .then((data) => {
                        updateDataTable(data);
                    })
                    .catch((error) => {
                        console.error("Error searching data:", error);
                    });
            }

            // 更新数据表格
            function updateDataTable(data) {
                const tbody = document.querySelector(".card table tbody");
                tbody.innerHTML = "";

                if (data.length === 0) {
                    const row = document.createElement("tr");
                    row.innerHTML =
                        '<td colspan="6" style="text-align: center;">暂无数据</td>';
                    tbody.appendChild(row);
                    return;
                }

                data.forEach((item) => {
                    const row = document.createElement("tr");
                    row.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${getCategoryText(item.category)}</td>
                        <td>¥${item.price.toFixed(2)}</td>
                        <td>${getStatusText(item.status)}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-warning" onclick="editItem(${
                                    item.id
                                })">修改</button>
                                <button onclick="viewItem(${
                                    item.id
                                })">查看</button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 编辑项目
            function editItem(id) {
                loadItemForEdit(id);
            }

            // 加载要编辑的项目
            function loadItemForEdit(id) {
                fetch("/api/data")
                    .then((response) => response.json())
                    .then((data) => {
                        const item = data.find((d) => d.id === id);
                        if (item) {
                            fillForm(item);
                            currentEditId = id;
                        } else {
                            alert("数据不存在！");
                        }
                    })
                    .catch((error) => {
                        console.error("Error loading item:", error);
                    });
            }

            // 填充表单
            function fillForm(item) {
                document.getElementById("edit-id").value = item.id;
                document.getElementById("edit-name").value = item.name;
                document.getElementById("edit-category").value = item.category;
                document.getElementById("edit-description").value =
                    item.description || "";
                document.getElementById("edit-price").value = item.price;
                document.getElementById("edit-status").value = item.status;
            }

            // 保存编辑
            function saveEdit() {
                if (!currentEditId) {
                    alert("请先选择要修改的数据！");
                    return;
                }

                const formData = {
                    name: document.getElementById("edit-name").value,
                    category: document.getElementById("edit-category").value,
                    description:
                        document.getElementById("edit-description").value,
                    price:
                        parseFloat(
                            document.getElementById("edit-price").value
                        ) || 0,
                    status: document.getElementById("edit-status").value,
                };

                fetch(`/api/data/${currentEditId}`, {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(formData),
                })
                    .then((response) => response.json())
                    .then((data) => {
                        alert("数据修改成功！");
                        clearForm();
                        loadAllData();
                    })
                    .catch((error) => {
                        console.error("Error updating item:", error);
                        alert("修改失败，请重试！");
                    });
            }

            // 清空表单
            function clearForm() {
                document.getElementById("edit-form").reset();
                document.getElementById("edit-id").value = "";
                currentEditId = null;
            }

            // 查看项目
            function viewItem(id) {
                alert(`查看功能 - ID: ${id}`);
            }

            // 获取类别中文名称
            function getCategoryText(category) {
                const categoryMap = {
                    product: "产品",
                    service: "服务",
                    technology: "技术",
                    other: "其他",
                };
                return categoryMap[category] || category;
            }

            // 获取状态中文名称
            function getStatusText(status) {
                const statusMap = {
                    normal: "正常",
                    pause: "暂停",
                    stop: "停止",
                };
                return statusMap[status] || status;
            }
        </script>
    </body>
</html>
