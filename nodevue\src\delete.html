<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>删除页面 - 多页面管理系统</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background-color: #f5f7fa;
                color: #333;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            header {
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                color: white;
                padding: 20px 0;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .logo {
                font-size: 28px;
                font-weight: bold;
            }

            nav ul {
                display: flex;
                list-style: none;
            }

            nav li {
                margin-left: 20px;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 8px 15px;
                border-radius: 5px;
                transition: background-color 0.3s;
            }

            nav a:hover,
            nav a.active {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .card {
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                padding: 20px;
                margin-bottom: 20px;
            }

            h2 {
                color: #2c3e50;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #f0f0f0;
            }

            .card h3 {
                color: #3498db;
                margin-bottom: 15px;
            }

            .search-box {
                display: flex;
                margin-bottom: 20px;
            }

            .search-box input {
                flex: 1;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            .search-box button {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            button {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #2980b9;
            }

            .btn-danger {
                background: #e74c3c;
            }

            .btn-danger:hover {
                background: #c0392b;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }

            th,
            td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }

            th {
                background-color: #f8f9fa;
                font-weight: 600;
            }

            tr:hover {
                background-color: #f5f7fa;
            }

            .action-buttons {
                display: flex;
                gap: 10px;
            }

            .action-buttons button {
                padding: 8px 12px;
                font-size: 14px;
            }

            footer {
                text-align: center;
                margin-top: 50px;
                padding: 20px;
                color: #7f8c8d;
                border-top: 1px solid #eee;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <div class="header-content">
                    <div class="logo">多页面管理系统</div>
                    <nav>
                        <ul>
                            <li>
                                <a href="/index.html">首页</a>
                            </li>
                            <li>
                                <a href="/add.html">增加页面</a>
                            </li>
                            <li>
                                <a href="/delete.html" class="active"
                                    >删除页面</a
                                >
                            </li>
                            <li>
                                <a href="/edit.html">修改页面</a>
                            </li>
                            <li>
                                <a href="/search.html">查询页面</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>

            <main>
                <h2>删除数据</h2>
                <div class="card">
                    <h3>选择要删除的数据</h3>
                    <div class="search-box">
                        <input type="text" placeholder="搜索数据..." />
                        <button>搜索</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="select-all" />
                                </th>
                                <th>ID</th>
                                <th>名称</th>
                                <th>类别</th>
                                <th>价格</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox" /></td>
                                <td>1001</td>
                                <td>示例产品A</td>
                                <td>产品</td>
                                <td>¥199.00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-danger">删除</button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" /></td>
                                <td>1002</td>
                                <td>技术服务B</td>
                                <td>服务</td>
                                <td>¥599.00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-danger">删除</button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" /></td>
                                <td>1003</td>
                                <td>技术方案C</td>
                                <td>技术</td>
                                <td>¥1299.00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-danger">删除</button>
                                        <button>查看</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="margin-top: 20px">
                        <button class="btn-danger" id="delete-selected">
                            删除选中项
                        </button>
                        <button style="margin-left: 10px" id="cancel-selection">
                            取消选择
                        </button>
                    </div>
                </div>

                <div class="card">
                    <h3>删除记录</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>删除时间</th>
                                <th>操作人</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1000</td>
                                <td>旧产品X</td>
                                <td>2023-05-10</td>
                                <td>管理员</td>
                            </tr>
                            <tr>
                                <td>0999</td>
                                <td>过时服务Y</td>
                                <td>2023-05-05</td>
                                <td>管理员</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </main>

            <footer>
                <p>© 2023 多页面管理系统 | 设计用于演示多页面切换功能</p>
            </footer>
        </div>

        <script>
            document.addEventListener("DOMContentLoaded", function () {
                loadAllData();
                setupEventListeners();
            });

            // 设置事件监听器
            function setupEventListeners() {
                // 全选功能
                document
                    .getElementById("select-all")
                    .addEventListener("change", function () {
                        const checkboxes = document.querySelectorAll(
                            'tbody input[type="checkbox"]'
                        );
                        checkboxes.forEach((checkbox) => {
                            checkbox.checked = this.checked;
                        });
                    });

                // 删除选中项按钮
                document
                    .getElementById("delete-selected")
                    .addEventListener("click", deleteSelected);

                // 取消选择按钮
                document
                    .getElementById("cancel-selection")
                    .addEventListener("click", function () {
                        document.getElementById("select-all").checked = false;
                        const checkboxes = document.querySelectorAll(
                            'tbody input[type="checkbox"]'
                        );
                        checkboxes.forEach(
                            (checkbox) => (checkbox.checked = false)
                        );
                    });

                // 搜索功能
                document
                    .querySelector(".search-box button")
                    .addEventListener("click", searchData);
            }

            // 加载所有数据
            function loadAllData() {
                fetch("/api/data")
                    .then((response) => response.json())
                    .then((data) => {
                        updateDataTable(data);
                    })
                    .catch((error) => {
                        console.error("Error loading data:", error);
                    });
            }

            // 搜索数据
            function searchData() {
                const keyword =
                    document.querySelector(".search-box input").value;
                if (!keyword.trim()) {
                    loadAllData();
                    return;
                }

                fetch(`/api/search?keyword=${encodeURIComponent(keyword)}`)
                    .then((response) => response.json())
                    .then((data) => {
                        updateDataTable(data);
                    })
                    .catch((error) => {
                        console.error("Error searching data:", error);
                    });
            }

            // 更新数据表格
            function updateDataTable(data) {
                const tbody = document.querySelector(".card table tbody");
                tbody.innerHTML = "";

                if (data.length === 0) {
                    const row = document.createElement("tr");
                    row.innerHTML =
                        '<td colspan="6" style="text-align: center;">暂无数据</td>';
                    tbody.appendChild(row);
                    return;
                }

                data.forEach((item) => {
                    const row = document.createElement("tr");
                    row.innerHTML = `
                        <td><input type="checkbox" data-id="${item.id}"></td>
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${getCategoryText(item.category)}</td>
                        <td>¥${item.price.toFixed(2)}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-danger" onclick="deleteItem(${
                                    item.id
                                })">删除</button>
                                <button onclick="viewItem(${
                                    item.id
                                })">查看</button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 删除单个项目
            function deleteItem(id) {
                if (confirm("确定要删除这条数据吗？")) {
                    fetch(`/api/data/${id}`, {
                        method: "DELETE",
                    })
                        .then((response) => response.json())
                        .then((data) => {
                            alert("数据删除成功！");
                            loadAllData();
                        })
                        .catch((error) => {
                            console.error("Error deleting item:", error);
                            alert("删除失败，请重试！");
                        });
                }
            }

            // 删除选中项目
            function deleteSelected() {
                const checkedBoxes = document.querySelectorAll(
                    'tbody input[type="checkbox"]:checked'
                );
                if (checkedBoxes.length === 0) {
                    alert("请选择要删除的数据！");
                    return;
                }

                if (
                    confirm(
                        `确定要删除选中的 ${checkedBoxes.length} 条数据吗？`
                    )
                ) {
                    const deletePromises = Array.from(checkedBoxes).map(
                        (checkbox) => {
                            const id = checkbox.getAttribute("data-id");
                            return fetch(`/api/data/${id}`, {
                                method: "DELETE",
                            });
                        }
                    );

                    Promise.all(deletePromises)
                        .then(() => {
                            alert("批量删除成功！");
                            loadAllData();
                            document.getElementById(
                                "select-all"
                            ).checked = false;
                        })
                        .catch((error) => {
                            console.error("Error deleting items:", error);
                            alert("删除失败，请重试！");
                        });
                }
            }

            // 查看项目
            function viewItem(id) {
                alert(`查看功能 - ID: ${id}`);
            }

            // 获取类别中文名称
            function getCategoryText(category) {
                const categoryMap = {
                    product: "产品",
                    service: "服务",
                    technology: "技术",
                    other: "其他",
                };
                return categoryMap[category] || category;
            }
        </script>
    </body>
</html>
