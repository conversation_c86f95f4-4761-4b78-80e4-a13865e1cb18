<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>首页 - 多页面管理系统</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background-color: #f5f7fa;
                color: #333;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            header {
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                color: white;
                padding: 20px 0;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .logo {
                font-size: 28px;
                font-weight: bold;
            }

            nav ul {
                display: flex;
                list-style: none;
            }

            nav li {
                margin-left: 20px;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 8px 15px;
                border-radius: 5px;
                transition: background-color 0.3s;
            }

            nav a:hover,
            nav a.active {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .welcome-section {
                text-align: center;
                padding: 40px 0;
            }

            .welcome-section h2 {
                font-size: 32px;
                margin-bottom: 20px;
                color: #2c3e50;
            }

            .welcome-section p {
                font-size: 18px;
                max-width: 700px;
                margin: 0 auto 30px;
                color: #7f8c8d;
            }

            button {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #2980b9;
            }

            .feature-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-top: 40px;
            }

            .feature-card {
                background: white;
                border-radius: 10px;
                padding: 25px;
                text-align: center;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                transition: transform 0.3s, box-shadow 0.3s;
            }

            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            }

            .feature-card i {
                font-size: 40px;
                margin-bottom: 15px;
                color: #3498db;
            }

            .feature-card h3 {
                margin-bottom: 15px;
                color: #2c3e50;
            }

            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-top: 40px;
            }

            .stat-card {
                background: white;
                border-radius: 10px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            }

            .stat-card h3 {
                font-size: 32px;
                color: #3498db;
                margin-bottom: 10px;
            }

            .stat-card p {
                color: #7f8c8d;
            }

            footer {
                text-align: center;
                margin-top: 50px;
                padding: 20px;
                color: #7f8c8d;
                border-top: 1px solid #eee;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <div class="header-content">
                    <div class="logo">多页面管理系统</div>
                    <nav>
                        <ul>
                            <li>
                                <a href="/index.html" class="active">首页</a>
                            </li>
                            <li>
                                <a href="/add.html">增加页面</a>
                            </li>
                            <li>
                                <a href="/delete.html">删除页面</a>
                            </li>
                            <li>
                                <a href="/edit.html">修改页面</a>
                            </li>
                            <li>
                                <a href="/search.html">查询页面</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>

            <main>
                <div class="welcome-section">
                    <h2>欢迎使用多页面管理系统</h2>
                    <p>
                        这是一个功能完整的多页面管理系统，包含增加、删除、修改和查询功能，满足您的日常数据管理需求。
                    </p>
                    <button>开始使用</button>
                </div>

                <div class="feature-cards">
                    <div class="feature-card">
                        <i>📊</i>
                        <h3>数据统计</h3>
                        <p>直观展示系统数据概览和关键指标</p>
                    </div>
                    <div class="feature-card">
                        <i>➕</i>
                        <h3>数据添加</h3>
                        <p>快速添加新数据到系统中</p>
                    </div>
                    <div class="feature-card">
                        <i>✏️</i>
                        <h3>数据修改</h3>
                        <p>灵活修改已有数据信息</p>
                    </div>
                    <div class="feature-card">
                        <i>🔍</i>
                        <h3>数据查询</h3>
                        <p>快速查找和筛选系统数据</p>
                    </div>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <h3 id="total-count">0</h3>
                        <p>总数据量</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="today-count">0</h3>
                        <p>今日新增</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="normal-count">0</h3>
                        <p>正常状态</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="pause-count">0</h3>
                        <p>暂停状态</p>
                    </div>
                </div>
            </main>

            <footer>
                <p>© 2023 多页面管理系统 | 设计用于演示多页面切换功能</p>
            </footer>
        </div>

        <script>
            // 页面加载时获取数据统计
            document.addEventListener("DOMContentLoaded", function () {
                loadStatistics();
            });

            // 加载统计数据
            function loadStatistics() {
                fetch("/api/data")
                    .then((response) => response.json())
                    .then((data) => {
                        updateStatistics(data);
                    })
                    .catch((error) => {
                        console.error("Error loading data:", error);
                    });
            }

            // 更新统计数据
            function updateStatistics(data) {
                const today = new Date().toISOString().split("T")[0];

                // 总数据量
                document.getElementById("total-count").textContent =
                    data.length;

                // 今日新增
                const todayCount = data.filter(
                    (item) => item.createTime === today
                ).length;
                document.getElementById("today-count").textContent = todayCount;

                // 正常状态
                const normalCount = data.filter(
                    (item) => item.status === "normal"
                ).length;
                document.getElementById("normal-count").textContent =
                    normalCount;

                // 暂停状态
                const pauseCount = data.filter(
                    (item) => item.status === "pause"
                ).length;
                document.getElementById("pause-count").textContent = pauseCount;
            }
        </script>
    </body>
</html>
